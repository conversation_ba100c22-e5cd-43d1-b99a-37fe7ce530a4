{"compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "module": "commonjs", "moduleResolution": "node", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"@synapseai/shared": ["packages/shared/src"]}}, "exclude": ["node_modules", "dist", "**/*.spec.ts", "**/*.test.ts"]}