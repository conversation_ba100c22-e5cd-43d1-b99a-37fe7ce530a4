import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Bot,
  Wrench,
  GitBranch,
  Settings,
  MessageSquare,
  Database,
  Code,
  BarChart3,
  Plus,
  Search,
  Bell,
  User,
  Activity,
  Zap,
  Brain,
  Globe,
} from "lucide-react";

interface DashboardProps {
  activeSection?: string;
}

export default function Dashboard({
  activeSection = "overview",
}: DashboardProps) {
  const navigationItems = [
    { id: "overview", label: "Dashboard", icon: BarChart3 },
    { id: "agents", label: "Agent Builder", icon: Bot },
    { id: "tools", label: "Tool Manager", icon: Wrench },
    { id: "tool-agents", label: "Tool Agent Builder", icon: GitBranch },
    { id: "providers", label: "Provider Manager", icon: Settings },
    { id: "sessions", label: "Session Management", icon: MessageSquare },
    { id: "knowledge", label: "Knowledge Base", icon: Database },
    { id: "widgets", label: "Widget Generator", icon: Code },
    { id: "analytics", label: "Analytics", icon: BarChart3 },
  ];

  const quickStats = [
    { label: "Active Agents", value: "12", change: "+3", icon: Bot },
    { label: "Tools Created", value: "28", change: "+5", icon: Wrench },
    { label: "Sessions Today", value: "156", change: "+12%", icon: Activity },
    { label: "API Calls", value: "2.4k", change: "+8%", icon: Zap },
  ];

  const recentAgents = [
    {
      name: "Customer Support Bot",
      status: "Active",
      lastUsed: "2 hours ago",
      type: "Chat Agent",
    },
    {
      name: "Data Analyzer",
      status: "Testing",
      lastUsed: "1 day ago",
      type: "Tool Agent",
    },
    {
      name: "Content Generator",
      status: "Active",
      lastUsed: "30 minutes ago",
      type: "Creative Agent",
    },
  ];

  const recentTools = [
    {
      name: "Email Validator",
      category: "Utility",
      usage: "High",
      lastUpdated: "Yesterday",
    },
    {
      name: "PDF Parser",
      category: "Document",
      usage: "Medium",
      lastUpdated: "3 days ago",
    },
    {
      name: "Image Resizer",
      category: "Media",
      usage: "Low",
      lastUpdated: "1 week ago",
    },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-card">
        <div className="flex h-16 items-center px-6">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Brain className="h-8 w-8 text-primary" />
              <h1 className="text-2xl font-bold">SynapseAI</h1>
            </div>
          </div>

          <div className="ml-auto flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search agents, tools..."
                className="pl-8 w-64"
              />
            </div>
            <Button variant="ghost" size="icon">
              <Bell className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon">
              <User className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <aside className="w-64 border-r bg-card h-[calc(100vh-4rem)]">
          <nav className="p-4 space-y-2">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              return (
                <Button
                  key={item.id}
                  variant={activeSection === item.id ? "default" : "ghost"}
                  className="w-full justify-start"
                >
                  <Icon className="mr-2 h-4 w-4" />
                  {item.label}
                </Button>
              );
            })}
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-6">
          {activeSection === "overview" && (
            <div className="space-y-6">
              {/* Page Header */}
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-3xl font-bold tracking-tight">
                    Dashboard
                  </h2>
                  <p className="text-muted-foreground">
                    Welcome to your AI orchestration platform
                  </p>
                </div>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Create New
                </Button>
              </div>

              {/* Quick Stats */}
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                {quickStats.map((stat, index) => {
                  const Icon = stat.icon;
                  return (
                    <Card key={index}>
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">
                          {stat.label}
                        </CardTitle>
                        <Icon className="h-4 w-4 text-muted-foreground" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">{stat.value}</div>
                        <p className="text-xs text-muted-foreground">
                          <span className="text-green-600">{stat.change}</span>{" "}
                          from last month
                        </p>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>

              {/* Recent Activity */}
              <div className="grid gap-6 md:grid-cols-2">
                {/* Recent Agents */}
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Agents</CardTitle>
                    <CardDescription>
                      Your most recently used AI agents
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {recentAgents.map((agent, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between"
                        >
                          <div className="space-y-1">
                            <p className="text-sm font-medium">{agent.name}</p>
                            <p className="text-xs text-muted-foreground">
                              {agent.type} • {agent.lastUsed}
                            </p>
                          </div>
                          <Badge
                            variant={
                              agent.status === "Active"
                                ? "default"
                                : "secondary"
                            }
                          >
                            {agent.status}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Recent Tools */}
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Tools</CardTitle>
                    <CardDescription>
                      Your most recently updated tools
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {recentTools.map((tool, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between"
                        >
                          <div className="space-y-1">
                            <p className="text-sm font-medium">{tool.name}</p>
                            <p className="text-xs text-muted-foreground">
                              {tool.category} • {tool.lastUpdated}
                            </p>
                          </div>
                          <Badge
                            variant={
                              tool.usage === "High"
                                ? "default"
                                : tool.usage === "Medium"
                                  ? "secondary"
                                  : "outline"
                            }
                          >
                            {tool.usage}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                  <CardDescription>
                    Get started with common tasks
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-3">
                    <Button variant="outline" className="h-20 flex-col">
                      <Bot className="h-6 w-6 mb-2" />
                      Create Agent
                    </Button>
                    <Button variant="outline" className="h-20 flex-col">
                      <Wrench className="h-6 w-6 mb-2" />
                      Build Tool
                    </Button>
                    <Button variant="outline" className="h-20 flex-col">
                      <Globe className="h-6 w-6 mb-2" />
                      Generate Widget
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {activeSection === "agents" && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-3xl font-bold tracking-tight">
                    Agent Builder
                  </h2>
                  <p className="text-muted-foreground">
                    Create and manage your AI agents
                  </p>
                </div>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  New Agent
                </Button>
              </div>

              <Tabs defaultValue="all" className="space-y-4">
                <TabsList>
                  <TabsTrigger value="all">All Agents</TabsTrigger>
                  <TabsTrigger value="active">Active</TabsTrigger>
                  <TabsTrigger value="testing">Testing</TabsTrigger>
                  <TabsTrigger value="draft">Draft</TabsTrigger>
                </TabsList>

                <TabsContent value="all" className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {[1, 2, 3, 4, 5, 6].map((i) => (
                      <Card key={i}>
                        <CardHeader>
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-lg">Agent {i}</CardTitle>
                            <Badge>Active</Badge>
                          </div>
                          <CardDescription>
                            AI agent for customer support and assistance
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span className="text-muted-foreground">
                                Provider:
                              </span>
                              <span>OpenAI GPT-4</span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-muted-foreground">
                                Sessions:
                              </span>
                              <span>24 today</span>
                            </div>
                            <Separator />
                            <div className="flex gap-2">
                              <Button size="sm" variant="outline">
                                Edit
                              </Button>
                              <Button size="sm" variant="outline">
                                Test
                              </Button>
                              <Button size="sm" variant="outline">
                                Deploy
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          )}

          {activeSection === "tools" && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-3xl font-bold tracking-tight">
                    Tool Manager
                  </h2>
                  <p className="text-muted-foreground">
                    Build and manage stateless task APIs
                  </p>
                </div>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  New Tool
                </Button>
              </div>

              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {[
                  "Email Validator",
                  "PDF Parser",
                  "Image Resizer",
                  "Text Summarizer",
                  "Data Converter",
                  "API Connector",
                ].map((tool, i) => (
                  <Card key={i}>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg">{tool}</CardTitle>
                        <Badge variant="secondary">Tool</Badge>
                      </div>
                      <CardDescription>
                        Stateless API for {tool.toLowerCase()} functionality
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">
                            Category:
                          </span>
                          <span>Utility</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Usage:</span>
                          <span>High</span>
                        </div>
                        <Separator />
                        <div className="flex gap-2">
                          <Button size="sm" variant="outline">
                            Edit
                          </Button>
                          <Button size="sm" variant="outline">
                            Test
                          </Button>
                          <Button size="sm" variant="outline">
                            API
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
}
