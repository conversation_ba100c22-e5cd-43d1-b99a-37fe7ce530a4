{"name": "@synapseai/shared", "version": "0.1.0", "description": "Shared types and utilities for SynapseAI", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src/**/*.ts --fix", "test": "jest"}, "dependencies": {"class-validator": "^0.14.0", "class-transformer": "^0.5.1"}, "devDependencies": {"@types/node": "^20.3.1", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.1.3"}}