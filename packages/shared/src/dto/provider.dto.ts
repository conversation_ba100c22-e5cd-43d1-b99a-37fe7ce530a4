import { 
  IsString, 
  IsEnum, 
  IsObject, 
  IsBoolean, 
  IsOptional, 
  IsNumber, 
  IsArray, 
  ValidateNested,
  Min,
  Max,
  IsUrl,
  Matches
} from 'class-validator';
import { Type } from 'class-transformer';
import { ProviderType, Status } from '../types/provider.types';

export class RateLimitsDto {
  @IsNumber()
  @Min(1)
  requestsPerMinute: number;

  @IsNumber()
  @Min(1)
  tokensPerMinute: number;

  @IsNumber()
  @Min(1)
  dailyLimit: number;
}

export class RoutingConditionDto {
  @IsEnum(['model', 'cost', 'latency', 'availability'])
  type: 'model' | 'cost' | 'latency' | 'availability';

  @IsEnum(['eq', 'lt', 'gt', 'lte', 'gte'])
  operator: 'eq' | 'lt' | 'gt' | 'lte' | 'gte';

  @IsString()
  value: any;
}

export class RoutingConfigDto {
  @IsNumber()
  @Min(0)
  @Max(100)
  priority: number;

  @IsArray()
  @IsString({ each: true })
  fallbackProviders: string[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RoutingConditionDto)
  conditions: RoutingConditionDto[];
}

export class ProviderConfigurationDto {
  @IsString()
  @Matches(/^[a-zA-Z0-9\-_]+$/, { message: 'API key contains invalid characters' })
  apiKey: string;

  @IsOptional()
  @IsUrl()
  baseUrl?: string;

  @IsString()
  model: string;

  @IsNumber()
  @Min(1)
  @Max(32000)
  maxTokens: number;

  @IsNumber()
  @Min(0)
  @Max(2)
  temperature: number;

  @ValidateNested()
  @Type(() => RateLimitsDto)
  rateLimits: RateLimitsDto;

  @ValidateNested()
  @Type(() => RoutingConfigDto)
  routing: RoutingConfigDto;
}

export class CreateProviderDto {
  @IsString()
  name: string;

  @IsEnum(ProviderType)
  type: ProviderType;

  @ValidateNested()
  @Type(() => ProviderConfigurationDto)
  configuration: ProviderConfigurationDto;

  @IsOptional()
  @IsBoolean()
  isDefault?: boolean;
}

export class UpdateProviderDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsEnum(Status)
  status?: Status;

  @IsOptional()
  @ValidateNested()
  @Type(() => ProviderConfigurationDto)
  configuration?: Partial<ProviderConfigurationDto>;

  @IsOptional()
  @IsBoolean()
  isDefault?: boolean;
}

export class ProviderUsageDto {
  @IsOptional()
  @IsNumber()
  @Min(0)
  requests?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  tokens?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  cost?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  errors?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  latency?: number;
}
