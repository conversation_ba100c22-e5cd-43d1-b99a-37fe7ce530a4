import { BaseEntity, Status } from './common.types';

export interface Agent extends BaseEntity {
  name: string;
  description: string;
  status: Status;
  configuration: AgentConfiguration;
  userId: string;
  version: number;
}

export interface AgentConfiguration {
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  memory: AgentMemory;
  tools: string[];
  knowledgeBase?: string[];
}

export interface AgentMemory {
  type: 'short-term' | 'long-term' | 'hybrid';
  maxMessages: number;
  persistAcrossSessions: boolean;
}

export interface AgentExecution {
  id: string;
  agentId: string;
  sessionId: string;
  input: any;
  output: any;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startedAt: Date;
  completedAt?: Date;
  error?: string;
}
