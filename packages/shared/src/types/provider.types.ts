import { BaseEntity, Status } from './common.types';

export interface AIProvider extends BaseEntity {
  name: string;
  type: ProviderType;
  status: Status;
  configuration: ProviderConfiguration;
  userId: string;
  isDefault: boolean;
}

export enum ProviderType {
  OPENAI = 'openai',
  CLAUDE = 'claude',
  GEMINI = 'gemini',
  MISTRAL = 'mistral',
  GROQ = 'groq'
}

export interface ProviderConfiguration {
  apiKey: string;
  baseUrl?: string;
  model: string;
  maxTokens: number;
  temperature: number;
  rateLimits: RateLimits;
  routing: RoutingConfig;
}

export interface RateLimits {
  requestsPerMinute: number;
  tokensPerMinute: number;
  dailyLimit: number;
}

export interface RoutingConfig {
  priority: number;
  fallbackProviders: string[];
  conditions: RoutingCondition[];
}

export interface RoutingCondition {
  type: 'model' | 'cost' | 'latency' | 'availability';
  operator: 'eq' | 'lt' | 'gt' | 'lte' | 'gte';
  value: any;
}

export interface ProviderUsage {
  providerId: string;
  requests: number;
  tokens: number;
  cost: number;
  errors: number;
  avgLatency: number;
  date: Date;
}
