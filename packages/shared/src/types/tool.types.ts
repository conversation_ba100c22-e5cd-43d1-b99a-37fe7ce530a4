import { BaseEntity, Status } from './common.types';

export interface Tool extends BaseEntity {
  name: string;
  description: string;
  status: Status;
  configuration: ToolConfiguration;
  userId: string;
  version: number;
}

export interface ToolConfiguration {
  inputs: ToolInput[];
  outputs: ToolOutput[];
  endpoint?: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  authentication?: ToolAuthentication;
}

export interface ToolInput {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  required: boolean;
  description: string;
  validation?: any;
}

export interface ToolOutput {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  description: string;
}

export interface ToolAuthentication {
  type: 'none' | 'api-key' | 'bearer' | 'basic';
  credentials?: Record<string, string>;
}

export interface ToolExecution {
  id: string;
  toolId: string;
  sessionId: string;
  input: any;
  output: any;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startedAt: Date;
  completedAt?: Date;
  error?: string;
}
