export interface WebSocketMessage<T = any> {
  event: string;
  data: T;
  sessionId?: string;
  userId?: string;
  timestamp: Date;
}

export interface WebSocketResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  event: string;
}

// Event types
export interface SessionUpdateEvent {
  sessionId: string;
  type: 'message' | 'status' | 'context';
  payload: any;
}

export interface AgentExecutionEvent {
  executionId: string;
  agentId: string;
  sessionId: string;
  status: 'started' | 'progress' | 'completed' | 'failed';
  data?: any;
  error?: string;
}

export interface ToolExecutionEvent {
  executionId: string;
  toolId: string;
  sessionId: string;
  status: 'started' | 'progress' | 'completed' | 'failed';
  data?: any;
  error?: string;
}

export interface HumanInterventionEvent {
  sessionId: string;
  type: 'approval_required' | 'input_required' | 'review_required';
  context: any;
  timeout?: number;
}
