import { BaseEntity } from './common.types';

export interface Session extends BaseEntity {
  userId: string;
  agentId?: string;
  status: SessionStatus;
  metadata: SessionMetadata;
  messages: SessionMessage[];
  context: Record<string, any>;
}

export enum SessionStatus {
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

export interface SessionMetadata {
  title?: string;
  tags?: string[];
  priority: 'low' | 'medium' | 'high';
  humanInLoop: boolean;
}

export interface SessionMessage {
  id: string;
  sessionId: string;
  role: 'user' | 'assistant' | 'system' | 'tool';
  content: string;
  metadata?: Record<string, any>;
  timestamp: Date;
}

export interface SessionContext {
  variables: Record<string, any>;
  state: Record<string, any>;
  history: SessionMessage[];
}
