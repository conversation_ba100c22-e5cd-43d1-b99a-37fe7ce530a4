export const WEBSOCKET_EVENTS = {
  // Connection events
  CONNECTION: 'connection',
  DISCONNECT: 'disconnect',
  AUTHENTICATE: 'authenticate',
  
  // Session events
  SESSION_CREATE: 'session:create',
  SESSION_UPDATE: 'session:update',
  SESSION_DELETE: 'session:delete',
  SESSION_MESSAGE: 'session:message',
  
  // Agent events
  AGENT_EXECUTE: 'agent:execute',
  AGENT_EXECUTION_UPDATE: 'agent:execution:update',
  AGENT_EXECUTION_COMPLETE: 'agent:execution:complete',
  AGENT_EXECUTION_ERROR: 'agent:execution:error',
  
  // Tool events
  TOOL_EXECUTE: 'tool:execute',
  TOOL_EXECUTION_UPDATE: 'tool:execution:update',
  TOOL_EXECUTION_COMPLETE: 'tool:execution:complete',
  TOOL_EXECUTION_ERROR: 'tool:execution:error',
  
  // Human intervention events
  HUMAN_INTERVENTION_REQUIRED: 'human:intervention:required',
  HUMAN_INTERVENTION_RESPONSE: 'human:intervention:response',
  
  // System events
  SYSTEM_STATUS: 'system:status',
  SYSTEM_ERROR: 'system:error'
} as const;

export type WebSocketEvent = typeof WEBSOCKET_EVENTS[keyof typeof WEBSOCKET_EVENTS];
