import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { AIProvider } from './entities/provider.entity';
import { ProviderUsage } from './entities/provider-usage.entity';
import { RedisService } from '../redis/redis.service';
import type {
  PaginationQuery,
  PaginatedResponse,
  ProviderType,
  Status,
  CreateProviderDto,
  UpdateProviderDto,
  ProviderUsageDto
} from '@synapseai/shared';

export interface ProviderUsageMetrics {
  totalRequests: number;
  totalTokens: number;
  totalCost: number;
  totalErrors: number;
  avgLatency: number;
  successRate: number;
}

@Injectable()
export class ProviderService {
  private readonly logger = new Logger(ProviderService.name);
  private readonly CACHE_TTL = 300; // 5 minutes
  private readonly USAGE_CACHE_TTL = 60; // 1 minute

  constructor(
    @InjectRepository(AIProvider)
    private readonly providerRepository: Repository<AIProvider>,
    @InjectRepository(ProviderUsage)
    private readonly usageRepository: Repository<ProviderUsage>,
    private readonly redisService: RedisService,
  ) {}

  /**
   * Create a new AI provider for a user
   */
  async create(userId: string, createProviderDto: CreateProviderDto): Promise<AIProvider> {
    try {
      // Validate API key format based on provider type
      this.validateApiKey(createProviderDto.type, createProviderDto.configuration.apiKey);

      // If this is set as default, unset other defaults for this user
      if (createProviderDto.isDefault) {
        await this.unsetDefaultProviders(userId);
      }

      const provider = this.providerRepository.create({
        ...createProviderDto,
        userId,
        status: Status.ACTIVE,
      });

      const savedProvider = await this.providerRepository.save(provider);
      
      // Cache the provider
      await this.redisService.setCache(
        `provider:${savedProvider.id}`, 
        savedProvider, 
        this.CACHE_TTL
      );

      // Invalidate user providers cache
      await this.redisService.deleteCache(`user:${userId}:providers`);

      this.logger.log(`Created provider ${savedProvider.id} for user ${userId}`);
      return savedProvider;
    } catch (error) {
      this.logger.error(`Failed to create provider for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get all providers for a user with pagination
   */
  async findAll(userId: string, query: PaginationQuery): Promise<PaginatedResponse<AIProvider>> {
    const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'DESC' } = query;
    const skip = (page - 1) * limit;

    // Try cache first
    const cacheKey = `user:${userId}:providers:${page}:${limit}:${sortBy}:${sortOrder}`;
    const cached = await this.redisService.getCache(cacheKey);
    if (cached) {
      return cached;
    }

    const [providers, total] = await this.providerRepository.findAndCount({
      where: { userId },
      skip,
      take: limit,
      order: { [sortBy]: sortOrder },
      relations: ['usage'],
    });

    const result = {
      data: providers,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };

    // Cache the result
    await this.redisService.setCache(cacheKey, result, this.CACHE_TTL);

    return result;
  }

  /**
   * Get a specific provider by ID for a user
   */
  async findOne(id: string, userId: string): Promise<AIProvider> {
    // Try cache first
    const cached = await this.redisService.getCache(`provider:${id}`);
    if (cached && cached.userId === userId) {
      return cached;
    }

    const provider = await this.providerRepository.findOne({
      where: { id, userId },
      relations: ['usage'],
    });

    if (!provider) {
      throw new NotFoundException(`Provider with ID ${id} not found`);
    }

    // Cache the provider
    await this.redisService.setCache(`provider:${id}`, provider, this.CACHE_TTL);

    return provider;
  }

  /**
   * Update a provider
   */
  async update(id: string, userId: string, updateProviderDto: UpdateProviderDto): Promise<AIProvider> {
    const provider = await this.findOne(id, userId);

    // If setting as default, unset other defaults
    if (updateProviderDto.isDefault) {
      await this.unsetDefaultProviders(userId, id);
    }

    // Validate API key if being updated
    if (updateProviderDto.configuration?.apiKey) {
      this.validateApiKey(provider.type, updateProviderDto.configuration.apiKey);
    }

    // Merge configuration if provided
    if (updateProviderDto.configuration) {
      updateProviderDto.configuration = {
        ...provider.configuration,
        ...updateProviderDto.configuration,
      };
    }

    await this.providerRepository.update({ id, userId }, updateProviderDto);
    
    // Get updated provider
    const updatedProvider = await this.findOne(id, userId);
    
    // Update cache
    await this.redisService.setCache(`provider:${id}`, updatedProvider, this.CACHE_TTL);
    
    // Invalidate user providers cache
    await this.redisService.deleteCache(`user:${userId}:providers`);

    this.logger.log(`Updated provider ${id} for user ${userId}`);
    return updatedProvider;
  }

  /**
   * Delete a provider
   */
  async remove(id: string, userId: string): Promise<boolean> {
    const provider = await this.findOne(id, userId);
    
    const result = await this.providerRepository.delete({ id, userId });
    
    if (result.affected > 0) {
      // Clear caches
      await this.redisService.deleteCache(`provider:${id}`);
      await this.redisService.deleteCache(`user:${userId}:providers`);
      
      this.logger.log(`Deleted provider ${id} for user ${userId}`);
      return true;
    }
    
    return false;
  }

  /**
   * Get the default provider for a user
   */
  async getDefaultProvider(userId: string): Promise<AIProvider | null> {
    const cacheKey = `user:${userId}:default-provider`;
    const cached = await this.redisService.getCache(cacheKey);
    if (cached) {
      return cached;
    }

    const provider = await this.providerRepository.findOne({
      where: { userId, isDefault: true, status: Status.ACTIVE },
    });

    if (provider) {
      await this.redisService.setCache(cacheKey, provider, this.CACHE_TTL);
    }

    return provider;
  }

  /**
   * Get the best available provider based on routing conditions
   */
  async getBestProvider(userId: string, context?: any): Promise<AIProvider | null> {
    const providers = await this.providerRepository.find({
      where: { userId, status: Status.ACTIVE },
      order: { 'configuration.routing.priority': 'DESC' },
    });

    if (providers.length === 0) {
      return null;
    }

    // For now, return the highest priority provider
    // TODO: Implement smart routing based on conditions
    return providers[0];
  }

  /**
   * Record usage metrics for a provider
   */
  async recordUsage(providerId: string, metrics: ProviderUsageDto): Promise<void> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    try {
      // Find or create usage record for today
      let usage = await this.usageRepository.findOne({
        where: { providerId, date: today },
      });

      if (!usage) {
        usage = this.usageRepository.create({
          providerId,
          date: today,
          requests: 0,
          tokens: 0,
          cost: 0,
          errors: 0,
          avgLatency: 0,
        });
      }

      // Update metrics
      if (metrics.requests) usage.requests += metrics.requests;
      if (metrics.tokens) usage.tokens += metrics.tokens;
      if (metrics.cost) usage.cost += metrics.cost;
      if (metrics.errors) usage.errors += metrics.errors;
      
      // Calculate average latency
      if (metrics.latency) {
        const totalRequests = usage.requests + (metrics.requests || 0);
        usage.avgLatency = ((usage.avgLatency * usage.requests) + metrics.latency) / totalRequests;
      }

      await this.usageRepository.save(usage);

      // Invalidate usage cache
      await this.redisService.deleteCache(`provider:${providerId}:usage`);
      
    } catch (error) {
      this.logger.error(`Failed to record usage for provider ${providerId}:`, error);
    }
  }

  /**
   * Get usage metrics for a provider within a date range
   */
  async getUsageMetrics(
    providerId: string, 
    userId: string,
    startDate: Date, 
    endDate: Date
  ): Promise<ProviderUsageMetrics> {
    // Verify provider belongs to user
    await this.findOne(providerId, userId);

    const cacheKey = `provider:${providerId}:usage:${startDate.toISOString()}:${endDate.toISOString()}`;
    const cached = await this.redisService.getCache(cacheKey);
    if (cached) {
      return cached;
    }

    const usageRecords = await this.usageRepository.find({
      where: {
        providerId,
        date: Between(startDate, endDate),
      },
    });

    const metrics = usageRecords.reduce(
      (acc, record) => ({
        totalRequests: acc.totalRequests + record.requests,
        totalTokens: acc.totalTokens + record.tokens,
        totalCost: acc.totalCost + Number(record.cost),
        totalErrors: acc.totalErrors + record.errors,
        avgLatency: acc.avgLatency + Number(record.avgLatency),
      }),
      {
        totalRequests: 0,
        totalTokens: 0,
        totalCost: 0,
        totalErrors: 0,
        avgLatency: 0,
      }
    );

    // Calculate averages and success rate
    const recordCount = usageRecords.length;
    if (recordCount > 0) {
      metrics.avgLatency = metrics.avgLatency / recordCount;
    }

    const successRate = metrics.totalRequests > 0 
      ? ((metrics.totalRequests - metrics.totalErrors) / metrics.totalRequests) * 100 
      : 100;

    const result = { ...metrics, successRate };

    // Cache the result
    await this.redisService.setCache(cacheKey, result, this.USAGE_CACHE_TTL);

    return result;
  }

  /**
   * Private helper methods
   */
  private async unsetDefaultProviders(userId: string, excludeId?: string): Promise<void> {
    const whereClause: any = { userId, isDefault: true };
    if (excludeId) {
      whereClause.id = { $ne: excludeId };
    }

    await this.providerRepository.update(whereClause, { isDefault: false });
    
    // Clear default provider cache
    await this.redisService.deleteCache(`user:${userId}:default-provider`);
  }

  private validateApiKey(type: ProviderType, apiKey: string): void {
    if (!apiKey || apiKey.trim().length === 0) {
      throw new BadRequestException('API key is required');
    }

    // Basic validation patterns for different providers
    const patterns = {
      [ProviderType.OPENAI]: /^sk-[a-zA-Z0-9]{48}$/,
      [ProviderType.CLAUDE]: /^sk-ant-[a-zA-Z0-9-_]{95}$/,
      [ProviderType.GEMINI]: /^[a-zA-Z0-9-_]{39}$/,
      [ProviderType.MISTRAL]: /^[a-zA-Z0-9]{32}$/,
      [ProviderType.GROQ]: /^gsk_[a-zA-Z0-9]{52}$/,
    };

    const pattern = patterns[type];
    if (pattern && !pattern.test(apiKey)) {
      throw new BadRequestException(`Invalid API key format for ${type} provider`);
    }
  }
}
