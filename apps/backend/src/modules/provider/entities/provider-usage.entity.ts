import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { ProviderUsage as IProviderUsage } from '@synapseai/shared';
import { AIProvider } from './provider.entity';

@Entity('provider_usage')
@Index(['providerId', 'date'])
@Index(['providerId', 'createdAt'])
export class ProviderUsage implements IProviderUsage {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @Index()
  providerId: string;

  @Column({ type: 'int', default: 0 })
  requests: number;

  @Column({ type: 'bigint', default: 0 })
  tokens: number;

  @Column({ type: 'decimal', precision: 10, scale: 4, default: 0 })
  cost: number;

  @Column({ type: 'int', default: 0 })
  errors: number;

  @Column({ type: 'decimal', precision: 8, scale: 2, default: 0 })
  avgLatency: number;

  @Column({ type: 'date' })
  @Index()
  date: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => AIProvider, (provider) => provider.usage, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'providerId' })
  provider: AIProvider;
}