import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import {
  AIProvider as IAIProvider,
  ProviderType,
  ProviderConfiguration,
  Status,
} from '@synapseai/shared';
  import { ProviderUsage } from './provider-usage.entity';

@Entity('ai_providers')
export class AIProvider implements IAIProvider {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({
    type: 'enum',
    enum: ProviderType,
  })
  type: ProviderType;

  @Column({
    type: 'enum',
    enum: Status,
    default: Status.ACTIVE,
  })
  status: Status;

  @Column('jsonb')
  configuration: ProviderConfiguration;

  @Column()
  userId: string;

  @Column({ default: false })
  isDefault: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => ProviderUsage, (usage) => usage.provider)
  usage: ProviderUsage[];
}