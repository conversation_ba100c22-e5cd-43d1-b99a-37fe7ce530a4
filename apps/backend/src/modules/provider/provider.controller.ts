import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { ProviderService } from './provider.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import type {
  PaginationQuery,
  CreateProviderDto,
  UpdateProviderDto,
  ProviderUsageDto
} from '@synapseai/shared';

@ApiTags('AI Providers')
@Controller('providers')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ProviderController {
  constructor(private readonly providerService: ProviderService) {}

  @Post()
  @ApiOperation({ 
    summary: 'Create a new AI provider',
    description: 'Create a new AI provider configuration for the authenticated user'
  })
  @ApiResponse({ 
    status: HttpStatus.CREATED, 
    description: 'Provider created successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', format: 'uuid' },
        name: { type: 'string' },
        type: { type: 'string', enum: ['openai', 'claude', 'gemini', 'mistral', 'groq'] },
        status: { type: 'string', enum: ['active', 'inactive', 'draft', 'archived'] },
        isDefault: { type: 'boolean' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
      }
    }
  })
  @ApiResponse({ 
    status: HttpStatus.BAD_REQUEST, 
    description: 'Invalid input data or API key format' 
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Authentication required' 
  })
  @ApiBody({
    description: 'Provider configuration data',
    schema: {
      type: 'object',
      required: ['name', 'type', 'configuration'],
      properties: {
        name: { 
          type: 'string', 
          description: 'Human-readable name for the provider',
          example: 'My OpenAI GPT-4 Provider'
        },
        type: { 
          type: 'string', 
          enum: ['openai', 'claude', 'gemini', 'mistral', 'groq'],
          description: 'Type of AI provider'
        },
        configuration: {
          type: 'object',
          required: ['apiKey', 'model', 'maxTokens', 'temperature'],
          properties: {
            apiKey: { 
              type: 'string', 
              description: 'API key for the provider',
              example: 'sk-...'
            },
            baseUrl: { 
              type: 'string', 
              description: 'Custom base URL (optional)',
              example: 'https://api.openai.com/v1'
            },
            model: { 
              type: 'string', 
              description: 'Model identifier',
              example: 'gpt-4'
            },
            maxTokens: { 
              type: 'number', 
              description: 'Maximum tokens per request',
              example: 4096
            },
            temperature: { 
              type: 'number', 
              description: 'Temperature setting (0-2)',
              example: 0.7
            },
            rateLimits: {
              type: 'object',
              properties: {
                requestsPerMinute: { type: 'number', example: 60 },
                tokensPerMinute: { type: 'number', example: 40000 },
                dailyLimit: { type: 'number', example: 1000000 }
              }
            },
            routing: {
              type: 'object',
              properties: {
                priority: { type: 'number', example: 1 },
                fallbackProviders: { 
                  type: 'array', 
                  items: { type: 'string' },
                  example: []
                },
                conditions: { 
                  type: 'array', 
                  items: { type: 'object' },
                  example: []
                }
              }
            }
          }
        },
        isDefault: { 
          type: 'boolean', 
          description: 'Set as default provider for the user',
          default: false
        }
      }
    }
  })
  async create(
    @Request() req,
    @Body(ValidationPipe) createProviderDto: CreateProviderDto
  ) {
    return this.providerService.create(req.user.sub, createProviderDto);
  }

  @Get()
  @ApiOperation({ 
    summary: 'Get all AI providers',
    description: 'Retrieve all AI providers for the authenticated user with pagination'
  })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Providers retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { type: 'object' }
        },
        total: { type: 'number' },
        page: { type: 'number' },
        limit: { type: 'number' },
        totalPages: { type: 'number' }
      }
    }
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 10)' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Sort field (default: createdAt)' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['ASC', 'DESC'], description: 'Sort order (default: DESC)' })
  async findAll(
    @Request() req,
    @Query() query: PaginationQuery
  ) {
    return this.providerService.findAll(req.user.sub, query);
  }

  @Get('default')
  @ApiOperation({ 
    summary: 'Get default AI provider',
    description: 'Get the default AI provider for the authenticated user'
  })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Default provider retrieved successfully' 
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'No default provider found' 
  })
  async getDefault(@Request() req) {
    const provider = await this.providerService.getDefaultProvider(req.user.sub);
    if (!provider) {
      return { message: 'No default provider configured' };
    }
    return provider;
  }

  @Get('best')
  @ApiOperation({ 
    summary: 'Get best available provider',
    description: 'Get the best available provider based on routing conditions'
  })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Best provider retrieved successfully' 
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'No available providers found' 
  })
  async getBest(@Request() req, @Query('context') context?: string) {
    const parsedContext = context ? JSON.parse(context) : undefined;
    const provider = await this.providerService.getBestProvider(req.user.sub, parsedContext);
    if (!provider) {
      return { message: 'No available providers found' };
    }
    return provider;
  }

  @Get(':id')
  @ApiOperation({ 
    summary: 'Get AI provider by ID',
    description: 'Retrieve a specific AI provider by its ID'
  })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid', description: 'Provider ID' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Provider retrieved successfully' 
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'Provider not found' 
  })
  @ApiResponse({ 
    status: HttpStatus.UNAUTHORIZED, 
    description: 'Authentication required' 
  })
  async findOne(
    @Request() req,
    @Param('id', ParseUUIDPipe) id: string
  ) {
    return this.providerService.findOne(id, req.user.sub);
  }

  @Patch(':id')
  @ApiOperation({ 
    summary: 'Update AI provider',
    description: 'Update an existing AI provider configuration'
  })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid', description: 'Provider ID' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Provider updated successfully' 
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'Provider not found' 
  })
  @ApiResponse({ 
    status: HttpStatus.BAD_REQUEST, 
    description: 'Invalid input data' 
  })
  @ApiBody({
    description: 'Provider update data',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        status: { type: 'string', enum: ['active', 'inactive', 'draft', 'archived'] },
        configuration: {
          type: 'object',
          properties: {
            apiKey: { type: 'string' },
            baseUrl: { type: 'string' },
            model: { type: 'string' },
            maxTokens: { type: 'number' },
            temperature: { type: 'number' },
            rateLimits: { type: 'object' },
            routing: { type: 'object' }
          }
        },
        isDefault: { type: 'boolean' }
      }
    }
  })
  async update(
    @Request() req,
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateProviderDto: UpdateProviderDto
  ) {
    return this.providerService.update(id, req.user.sub, updateProviderDto);
  }

  @Delete(':id')
  @ApiOperation({ 
    summary: 'Delete AI provider',
    description: 'Delete an AI provider configuration'
  })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid', description: 'Provider ID' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Provider deleted successfully' 
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'Provider not found' 
  })
  async remove(
    @Request() req,
    @Param('id', ParseUUIDPipe) id: string
  ) {
    const deleted = await this.providerService.remove(id, req.user.sub);
    if (!deleted) {
      return { message: 'Provider not found' };
    }
    return { message: 'Provider deleted successfully' };
  }

  @Get(':id/usage')
  @ApiOperation({ 
    summary: 'Get provider usage metrics',
    description: 'Get usage metrics for a specific provider within a date range'
  })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid', description: 'Provider ID' })
  @ApiQuery({ name: 'startDate', type: String, description: 'Start date (ISO string)' })
  @ApiQuery({ name: 'endDate', type: String, description: 'End date (ISO string)' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Usage metrics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        totalRequests: { type: 'number' },
        totalTokens: { type: 'number' },
        totalCost: { type: 'number' },
        totalErrors: { type: 'number' },
        avgLatency: { type: 'number' },
        successRate: { type: 'number' }
      }
    }
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'Provider not found' 
  })
  @ApiResponse({ 
    status: HttpStatus.BAD_REQUEST, 
    description: 'Invalid date range' 
  })
  async getUsageMetrics(
    @Request() req,
    @Param('id', ParseUUIDPipe) id: string,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string
  ) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return { error: 'Invalid date format. Use ISO date strings.' };
    }
    
    if (start >= end) {
      return { error: 'Start date must be before end date.' };
    }

    return this.providerService.getUsageMetrics(id, req.user.sub, start, end);
  }

  @Post(':id/usage')
  @ApiOperation({
    summary: 'Record provider usage',
    description: 'Record usage metrics for a provider (internal use)'
  })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid', description: 'Provider ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Usage recorded successfully'
  })
  @ApiBody({
    description: 'Usage metrics to record',
    schema: {
      type: 'object',
      properties: {
        requests: { type: 'number' },
        tokens: { type: 'number' },
        cost: { type: 'number' },
        errors: { type: 'number' },
        latency: { type: 'number' }
      }
    }
  })
  async recordUsage(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) metrics: ProviderUsageDto
  ) {
    await this.providerService.recordUsage(id, metrics);
    return { message: 'Usage recorded successfully' };
  }
}
