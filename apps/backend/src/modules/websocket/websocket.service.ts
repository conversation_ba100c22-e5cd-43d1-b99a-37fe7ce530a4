import { Injectable, Logger } from '@nestjs/common';
import { Socket } from 'socket.io';
import { JwtService } from '@nestjs/jwt';
import { RedisService } from '../redis/redis.service';
import type { WebSocketResponse } from '@synapseai/shared';

@Injectable()
export class WebSocketService {
  private readonly logger = new Logger(WebSocketService.name);
  private readonly connectedClients = new Map<string, Socket>();
  private readonly userSessions = new Map<string, Set<string>>();

  constructor(
    private readonly jwtService: JwtService,
    private readonly redisService: RedisService,
  ) {}

  async handleConnection(client: Socket): Promise<void> {
    this.connectedClients.set(client.id, client);
    this.logger.log(`Client ${client.id} connected. Total clients: ${this.connectedClients.size}`);
  }

  async handleDisconnection(client: Socket): Promise<void> {
    const userId = client.data?.userId;
    
    if (userId) {
      const userSessions = this.userSessions.get(userId);
      if (userSessions) {
        userSessions.delete(client.id);
        if (userSessions.size === 0) {
          this.userSessions.delete(userId);
        }
      }
    }

    this.connectedClients.delete(client.id);
    this.logger.log(`Client ${client.id} disconnected. Total clients: ${this.connectedClients.size}`);
  }

  async authenticateClient(client: Socket, token: string): Promise<WebSocketResponse> {
    try {
      const payload = this.jwtService.verify(token);
      const userId = payload.sub;

      client.data = { userId, ...payload };
      client.join(`user:${userId}`);

      // Track user sessions
      if (!this.userSessions.has(userId)) {
        this.userSessions.set(userId, new Set());
      }
      this.userSessions.get(userId).add(client.id);

      this.logger.log(`Client ${client.id} authenticated as user ${userId}`);

      return {
        success: true,
        event: 'authenticate',
        data: { userId },
      };
    } catch (error) {
      this.logger.error(`Authentication failed for client ${client.id}:`, error.message);
      return {
        success: false,
        event: 'authenticate',
        error: 'Authentication failed',
      };
    }
  }

  async handleSessionCreate(client: Socket, data: any): Promise<WebSocketResponse> {
    try {
      const userId = client.data?.userId;
      if (!userId) {
        return {
          success: false,
          event: 'session:create',
          error: 'User not authenticated',
        };
      }

      // Join session room
      client.join(`session:${data.sessionId}`);
      
      this.logger.log(`User ${userId} joined session ${data.sessionId}`);

      return {
        success: true,
        event: 'session:create',
        data: { sessionId: data.sessionId },
      };
    } catch (error) {
      this.logger.error('Session create error:', error.message);
      return {
        success: false,
        event: 'session:create',
        error: error.message,
      };
    }
  }

  async handleSessionMessage(client: Socket, data: any): Promise<WebSocketResponse> {
    try {
      const userId = client.data?.userId;
      if (!userId) {
        return {
          success: false,
          event: 'session:message',
          error: 'User not authenticated',
        };
      }

      // Store message in Redis for real-time sync
      await this.redisService.publish(
        `session:${data.sessionId}:messages`,
        JSON.stringify({
          userId,
          message: data.message,
          timestamp: new Date(),
        }),
      );

      return {
        success: true,
        event: 'session:message',
        data: { messageId: data.messageId },
      };
    } catch (error) {
      this.logger.error('Session message error:', error.message);
      return {
        success: false,
        event: 'session:message',
        error: error.message,
      };
    }
  }

  async handleAgentExecute(client: Socket, data: any): Promise<WebSocketResponse> {
    try {
      const userId = client.data?.userId;
      if (!userId) {
        return {
          success: false,
          event: 'agent:execute',
          error: 'User not authenticated',
        };
      }

      // Queue agent execution
      await this.redisService.publish(
        'agent:execution:queue',
        JSON.stringify({
          userId,
          agentId: data.agentId,
          sessionId: data.sessionId,
          input: data.input,
          timestamp: new Date(),
        }),
      );

      return {
        success: true,
        event: 'agent:execute',
        data: { executionId: data.executionId },
      };
    } catch (error) {
      this.logger.error('Agent execute error:', error.message);
      return {
        success: false,
        event: 'agent:execute',
        error: error.message,
      };
    }
  }

  async handleToolExecute(client: Socket, data: any): Promise<WebSocketResponse> {
    try {
      const userId = client.data?.userId;
      if (!userId) {
        return {
          success: false,
          event: 'tool:execute',
          error: 'User not authenticated',
        };
      }

      // Queue tool execution
      await this.redisService.publish(
        'tool:execution:queue',
        JSON.stringify({
          userId,
          toolId: data.toolId,
          sessionId: data.sessionId,
          input: data.input,
          timestamp: new Date(),
        }),
      );

      return {
        success: true,
        event: 'tool:execute',
        data: { executionId: data.executionId },
      };
    } catch (error) {
      this.logger.error('Tool execute error:', error.message);
      return {
        success: false,
        event: 'tool:execute',
        error: error.message,
      };
    }
  }

  async handleHumanInterventionResponse(client: Socket, data: any): Promise<WebSocketResponse> {
    try {
      const userId = client.data?.userId;
      if (!userId) {
        return {
          success: false,
          event: 'human:intervention:response',
          error: 'User not authenticated',
        };
      }

      // Store human intervention response
      await this.redisService.set(
        `human:intervention:${data.interventionId}`,
        JSON.stringify({
          userId,
          response: data.response,
          timestamp: new Date(),
        }),
        300, // 5 minutes TTL
      );

      return {
        success: true,
        event: 'human:intervention:response',
        data: { interventionId: data.interventionId },
      };
    } catch (error) {
      this.logger.error('Human intervention response error:', error.message);
      return {
        success: false,
        event: 'human:intervention:response',
        error: error.message,
      };
    }
  }

  // Utility methods
  getConnectedClients(): number {
    return this.connectedClients.size;
  }

  getUserSessions(userId: string): number {
    return this.userSessions.get(userId)?.size || 0;
  }

  isUserConnected(userId: string): boolean {
    return this.userSessions.has(userId) && this.userSessions.get(userId).size > 0;
  }
}
