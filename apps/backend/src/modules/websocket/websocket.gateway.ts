import {
  WebSocketGateway as WSGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  ConnectedSocket,
  MessageBody,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Injectable, Logger, UseGuards } from '@nestjs/common';
import { WebSocketService } from './websocket.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { WEBSOCKET_EVENTS } from '@synapseai/shared';
import type {
  WebSocketMessage,
  SessionUpdateEvent,
  AgentExecutionEvent,
  ToolExecutionEvent,
  HumanInterventionEvent,
} from '@synapseai/shared';

@WSGateway({
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
  },
  namespace: '/api/ws',
})
@Injectable()
export class WebSocketGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(WebSocketGateway.name);

  constructor(private readonly webSocketService: WebSocketService) {}

  async handleConnection(client: Socket) {
    this.logger.log(`Client connected: ${client.id}`);
    await this.webSocketService.handleConnection(client);
  }

  async handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`);
    await this.webSocketService.handleDisconnection(client);
  }

  @SubscribeMessage(WEBSOCKET_EVENTS.AUTHENTICATE)
  @UseGuards(JwtAuthGuard)
  async handleAuthentication(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { token: string },
  ) {
    return this.webSocketService.authenticateClient(client, data.token);
  }

  @SubscribeMessage(WEBSOCKET_EVENTS.SESSION_CREATE)
  @UseGuards(JwtAuthGuard)
  async handleSessionCreate(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: any,
  ) {
    return this.webSocketService.handleSessionCreate(client, data);
  }

  @SubscribeMessage(WEBSOCKET_EVENTS.SESSION_MESSAGE)
  @UseGuards(JwtAuthGuard)
  async handleSessionMessage(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: any,
  ) {
    return this.webSocketService.handleSessionMessage(client, data);
  }

  @SubscribeMessage(WEBSOCKET_EVENTS.AGENT_EXECUTE)
  @UseGuards(JwtAuthGuard)
  async handleAgentExecute(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: any,
  ) {
    return this.webSocketService.handleAgentExecute(client, data);
  }

  @SubscribeMessage(WEBSOCKET_EVENTS.TOOL_EXECUTE)
  @UseGuards(JwtAuthGuard)
  async handleToolExecute(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: any,
  ) {
    return this.webSocketService.handleToolExecute(client, data);
  }

  @SubscribeMessage(WEBSOCKET_EVENTS.HUMAN_INTERVENTION_RESPONSE)
  @UseGuards(JwtAuthGuard)
  async handleHumanInterventionResponse(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: any,
  ) {
    return this.webSocketService.handleHumanInterventionResponse(client, data);
  }

  // Server-side event emitters
  emitSessionUpdate(sessionId: string, event: SessionUpdateEvent) {
    this.server.to(`session:${sessionId}`).emit(WEBSOCKET_EVENTS.SESSION_UPDATE, event);
  }

  emitAgentExecutionUpdate(sessionId: string, event: AgentExecutionEvent) {
    this.server.to(`session:${sessionId}`).emit(WEBSOCKET_EVENTS.AGENT_EXECUTION_UPDATE, event);
  }

  emitToolExecutionUpdate(sessionId: string, event: ToolExecutionEvent) {
    this.server.to(`session:${sessionId}`).emit(WEBSOCKET_EVENTS.TOOL_EXECUTION_UPDATE, event);
  }

  emitHumanInterventionRequired(sessionId: string, event: HumanInterventionEvent) {
    this.server.to(`session:${sessionId}`).emit(WEBSOCKET_EVENTS.HUMAN_INTERVENTION_REQUIRED, event);
  }

  emitSystemError(error: any) {
    this.server.emit(WEBSOCKET_EVENTS.SYSTEM_ERROR, error);
  }
}
