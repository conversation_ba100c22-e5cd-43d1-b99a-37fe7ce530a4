import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { RedisService } from '../redis/redis.service';
import * as bcrypt from 'bcrypt';

@Injectable()
export class AuthService {
  constructor(
    private readonly jwtService: JwtService,
    private readonly redisService: RedisService,
  ) {}

  async validateUser(email: string, password: string): Promise<any> {
    // TODO: Implement user validation with database
    // For now, return a mock user
    const user = { id: '1', email, name: 'Test User' };
    return user;
  }

  async login(user: any) {
    const payload = { email: user.email, sub: user.id, name: user.name };
    const token = this.jwtService.sign(payload);
    
    // Store session in Redis
    await this.redisService.setSession(user.id, {
      userId: user.id,
      email: user.email,
      name: user.name,
      loginTime: new Date(),
    });

    return {
      access_token: token,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
      },
    };
  }

  async logout(userId: string) {
    await this.redisService.deleteSession(userId);
    return { message: 'Logged out successfully' };
  }

  async validateToken(token: string) {
    try {
      const payload = this.jwtService.verify(token);
      const session = await this.redisService.getSession(payload.sub);
      
      if (!session) {
        return null;
      }

      return payload;
    } catch (error) {
      return null;
    }
  }

  async hashPassword(password: string): Promise<string> {
    const saltRounds = 10;
    return bcrypt.hash(password, saltRounds);
  }

  async comparePasswords(password: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(password, hashedPassword);
  }
}
