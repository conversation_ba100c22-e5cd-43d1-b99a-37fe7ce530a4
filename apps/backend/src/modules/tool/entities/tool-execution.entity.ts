import { <PERSON><PERSON><PERSON>, PrimaryGenerated<PERSON><PERSON>umn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { ToolExecution as IToolExecution } from '@synapseai/shared';

@Entity('tool_executions')
export class ToolExecution implements IToolExecution {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  toolId: string;

  @Column()
  sessionId: string;

  @Column('jsonb')
  input: any;

  @Column('jsonb', { nullable: true })
  output: any;

  @Column({
    type: 'enum',
    enum: ['pending', 'running', 'completed', 'failed'],
    default: 'pending',
  })
  status: 'pending' | 'running' | 'completed' | 'failed';

  @CreateDateColumn()
  startedAt: Date;

  @UpdateDateColumn()
  completedAt?: Date;

  @Column('text', { nullable: true })
  error?: string;
}
