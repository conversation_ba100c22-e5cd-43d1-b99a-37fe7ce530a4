import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Tool<PERSON>ontroller } from './tool.controller';
import { ToolService } from './tool.service';
import { Tool } from './entities/tool.entity';
import { ToolExecution } from './entities/tool-execution.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Tool, ToolExecution])],
  controllers: [ToolController],
  providers: [ToolService],
  exports: [ToolService],
})
export class ToolModule {}
