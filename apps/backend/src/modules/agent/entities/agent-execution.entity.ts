import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { AgentExecution as IAgentExecution } from '@synapseai/shared';

@Entity('agent_executions')
export class AgentExecution implements IAgentExecution {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  agentId: string;

  @Column()
  sessionId: string;

  @Column('jsonb')
  input: any;

  @Column('jsonb', { nullable: true })
  output: any;

  @Column({
    type: 'enum',
    enum: ['pending', 'running', 'completed', 'failed'],
    default: 'pending',
  })
  status: 'pending' | 'running' | 'completed' | 'failed';

  @CreateDateColumn()
  startedAt: Date;

  @UpdateDateColumn()
  completedAt?: Date;

  @Column('text', { nullable: true })
  error?: string;
}
