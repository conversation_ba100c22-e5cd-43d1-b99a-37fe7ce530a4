import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AgentService } from './agent.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import type { PaginationQuery } from '@synapseai/shared';

@ApiTags('Agents')
@Controller('agents')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AgentController {
  constructor(private readonly agentService: AgentService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new agent' })
  @ApiResponse({ status: 201, description: 'Agent created successfully' })
  async create(@Request() req, @Body() createAgentDto: any) {
    return this.agentService.create({
      ...createAgentDto,
      userId: req.user.sub,
    });
  }

  @Get()
  @ApiOperation({ summary: 'Get all agents for user' })
  @ApiResponse({ status: 200, description: 'Agents retrieved successfully' })
  async findAll(@Request() req, @Query() query: PaginationQuery) {
    return this.agentService.findAll(req.user.sub, query);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get agent by ID' })
  @ApiResponse({ status: 200, description: 'Agent retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  async findOne(@Request() req, @Param('id') id: string) {
    const agent = await this.agentService.findOne(id, req.user.sub);
    if (!agent) {
      throw new Error('Agent not found');
    }
    return agent;
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update agent' })
  @ApiResponse({ status: 200, description: 'Agent updated successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  async update(@Request() req, @Param('id') id: string, @Body() updateAgentDto: any) {
    const agent = await this.agentService.update(id, req.user.sub, updateAgentDto);
    if (!agent) {
      throw new Error('Agent not found');
    }
    return agent;
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete agent' })
  @ApiResponse({ status: 200, description: 'Agent deleted successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  async remove(@Request() req, @Param('id') id: string) {
    const deleted = await this.agentService.remove(id, req.user.sub);
    if (!deleted) {
      throw new Error('Agent not found');
    }
    return { message: 'Agent deleted successfully' };
  }

  @Post(':id/execute')
  @ApiOperation({ summary: 'Execute agent' })
  @ApiResponse({ status: 200, description: 'Agent execution started' })
  async execute(
    @Param('id') id: string,
    @Body() executeDto: { sessionId: string; input: any },
  ) {
    return this.agentService.execute(id, executeDto.sessionId, executeDto.input);
  }

  @Get(':id/executions')
  @ApiOperation({ summary: 'Get agent executions' })
  @ApiResponse({ status: 200, description: 'Executions retrieved successfully' })
  async getExecutions(@Param('id') id: string, @Query() query: PaginationQuery) {
    return this.agentService.getExecutions(id, query);
  }
}
