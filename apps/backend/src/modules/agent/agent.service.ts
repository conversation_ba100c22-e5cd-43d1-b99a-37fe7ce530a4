import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Agent } from './entities/agent.entity';
import { AgentExecution } from './entities/agent-execution.entity';
import { RedisService } from '../redis/redis.service';
import type { PaginationQuery, PaginatedResponse } from '@synapseai/shared';

@Injectable()
export class AgentService {
  constructor(
    @InjectRepository(Agent)
    private readonly agentRepository: Repository<Agent>,
    @InjectRepository(AgentExecution)
    private readonly executionRepository: Repository<AgentExecution>,
    private readonly redisService: RedisService,
  ) {}

  async create(agentData: Partial<Agent>): Promise<Agent> {
    const agent = this.agentRepository.create(agentData);
    const savedAgent = await this.agentRepository.save(agent);
    
    // Cache the agent
    await this.redisService.setCache(`agent:${savedAgent.id}`, savedAgent);
    
    return savedAgent;
  }

  async findAll(userId: string, query: PaginationQuery): Promise<PaginatedResponse<Agent>> {
    const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'DESC' } = query;
    const skip = (page - 1) * limit;

    const [agents, total] = await this.agentRepository.findAndCount({
      where: { userId },
      skip,
      take: limit,
      order: { [sortBy]: sortOrder },
    });

    return {
      data: agents,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(id: string, userId: string): Promise<Agent | null> {
    // Try cache first
    const cached = await this.redisService.getCache(`agent:${id}`);
    if (cached && cached.userId === userId) {
      return cached;
    }

    const agent = await this.agentRepository.findOne({
      where: { id, userId },
    });

    if (agent) {
      await this.redisService.setCache(`agent:${id}`, agent);
    }

    return agent;
  }

  async update(id: string, userId: string, updateData: Partial<Agent>): Promise<Agent | null> {
    const agent = await this.findOne(id, userId);
    if (!agent) {
      return null;
    }

    // Increment version on update
    updateData.version = agent.version + 1;
    
    await this.agentRepository.update({ id, userId }, updateData);
    const updatedAgent = await this.findOne(id, userId);
    
    // Update cache
    if (updatedAgent) {
      await this.redisService.setCache(`agent:${id}`, updatedAgent);
    }
    
    return updatedAgent;
  }

  async remove(id: string, userId: string): Promise<boolean> {
    const result = await this.agentRepository.delete({ id, userId });
    
    if (result.affected > 0) {
      await this.redisService.deleteCache(`agent:${id}`);
      return true;
    }
    
    return false;
  }

  async execute(agentId: string, sessionId: string, input: any): Promise<AgentExecution> {
    const execution = this.executionRepository.create({
      agentId,
      sessionId,
      input,
      status: 'pending',
    });

    const savedExecution = await this.executionRepository.save(execution);
    
    // Queue execution for processing
    await this.redisService.publish(
      'agent:execution:queue',
      JSON.stringify({
        executionId: savedExecution.id,
        agentId,
        sessionId,
        input,
      }),
    );

    return savedExecution;
  }

  async getExecutions(agentId: string, query: PaginationQuery): Promise<PaginatedResponse<AgentExecution>> {
    const { page = 1, limit = 10, sortBy = 'startedAt', sortOrder = 'DESC' } = query;
    const skip = (page - 1) * limit;

    const [executions, total] = await this.executionRepository.findAndCount({
      where: { agentId },
      skip,
      take: limit,
      order: { [sortBy]: sortOrder },
    });

    return {
      data: executions,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }
}
