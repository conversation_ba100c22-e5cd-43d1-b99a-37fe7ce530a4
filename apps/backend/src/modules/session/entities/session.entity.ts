import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { Session as ISession, SessionStatus, SessionMetadata } from '@synapseai/shared';
import { SessionMessage } from './session-message.entity';

@Entity('sessions')
export class Session implements Omit<ISession, 'messages'> {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @Column({ nullable: true })
  agentId?: string;

  @Column({
    type: 'enum',
    enum: SessionStatus,
    default: SessionStatus.ACTIVE,
  })
  status: SessionStatus;

  @Column('jsonb')
  metadata: SessionMetadata;

  @OneToMany(() => SessionMessage, message => message.session)
  messages: SessionMessage[];

  @Column('jsonb', { default: {} })
  context: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
