import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { SessionMessage as ISessionMessage } from '@synapseai/shared';
import { Session } from './session.entity';

@Entity('session_messages')
export class SessionMessage implements ISessionMessage {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  sessionId: string;

  @ManyToOne(() => Session, session => session.messages)
  @JoinColumn({ name: 'sessionId' })
  session: Session;

  @Column({
    type: 'enum',
    enum: ['user', 'assistant', 'system', 'tool'],
  })
  role: 'user' | 'assistant' | 'system' | 'tool';

  @Column('text')
  content: string;

  @Column('jsonb', { nullable: true })
  metadata?: Record<string, any>;

  @CreateDateColumn()
  timestamp: Date;
}
